// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugMenuWidget.h"
#include "CommonButtonBase.h"
#include "CommonActivatableWidgetSwitcher.h"

void UDebugMenuWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if( CommandsTabButton )
	{
		CommandsTabButton->OnClicked().AddUObject( this, &UDebugMenuWidget::OnCommandsTabButtonClick );
		TabButtonArray.Add( CommandsTabButton );
	}

	if( CameraTabButton )
	{
		CameraTabButton->OnClicked().AddUObject( this, &UDebugMenuWidget::OnCameraBtnClick );
		TabButtonArray.Add( CameraTabButton );
	}
}

void UDebugMenuWidget::OnCommandsTabButtonClick()
{
	OnTabButtonClick( 1, CommandsTabButton );
}

void UDebugMenuWidget::OnCameraBtnClick()
{
	OnTabButtonClick( 2, <PERSON><PERSON>ab<PERSON><PERSON><PERSON> );
}

void UDebugMenuWidget::OnTabButtonClick( int32 WidgetIndex, UCommonButtonBase* SelectedButton )
{
	for( UCommonButtonBase* Button : TabButtonArray )
	{
		if( Button != SelectedButton )
		{
			Button->SetIsSelected( false );
		}
	}

	int32 CurrentIndex = DebugSwitcher->GetActiveWidgetIndex();
	if( CurrentIndex == WidgetIndex )
	{
		DebugSwitcher->SetActiveWidgetIndex( 0 );
	}
	else
	{
		DebugSwitcher->SetActiveWidgetIndex( WidgetIndex );
	}
}