// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "Grid.generated.h"

struct FGridShapeDataStruct;


UCLASS()
class VICTOR_API AGrid : public AActor
{
	GENERATED_BODY()

	//Fields
private:
	//Components
	UPROPERTY( VisibleAnywhere, Category = "Components" )
	UInstancedStaticMeshComponent* InstancedStaticMesh;

	//Blueprint Values
	UPROPERTY( EditAnywhere, Category = Spawn )
	FVector CenterLocation = FVector( 0.0f, 0.0f, 0.0f );

	UPROPERTY( EditAnywhere, Category = Spawn )
	FVector TileSize = FVector( 200.0f, 200.0f, 100.0f );;

	UPROPERTY( EditAnywhere, Category = Spawn )
	FVector2D TileCount = FVector2D( 10, 10 );

	UPROPERTY( EditAnywhere, Category = Spawn )
	EGridShapeEnum Shape;

	//FDataTableRowHandle
	UPROPERTY( EditDefaultsOnly, meta = ( RowType = "GridShapeDataStruct" ), Category = Spawn )
	UDataTable* GridShapeDataTable;

	//Functions
public:
	// Sets default values for this actor's properties
	AGrid();

private:
	const FGridShapeDataStruct* SetStaticMeshValues();

	FVector2D GetAlignedTileCount();

	FVector FindGridBottomLeftLocation();

	FVector GetTileLocation( FVector BottomLeft, int XIndex, int YIndex );

	FRotator GetTriangleRotation( int XIndex, int YIndex );

	void SpawnGenericTiles( const FGridShapeDataStruct* GridShapeData, FVector BottomLeft );

	void SpawnHexTiles( const FGridShapeDataStruct* GridShapeData, FVector BottomLeft );

	void SpawnGrid();

	//Util Functions
	FVector SnapVectorToVector( FVector Vector1, FVector Vector2 );

	bool IsFloatEven( float LocalFloat );

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick( float DeltaTime ) override;

	virtual void OnConstruction( const FTransform& Transform ) override;

#pragma region Getters and Setters
#pragma endregion
};
