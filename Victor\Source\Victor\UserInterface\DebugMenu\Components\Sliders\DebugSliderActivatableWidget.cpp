// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugSliderActivatableWidget.h"
#include "CommonTextBlock.h"
#include "Components/SpinBox.h"

void UDebugSliderActivatableWidget::NativePreConstruct()
{
	Super::NativePreConstruct();

	if( DisplayName )
	{
		DisplayName->SetText( SliderName );
	}

	if( DebugSpinBox )
	{
		DebugSpinBox->SetMinSliderValue( MinMaxSliderValues.X );
		DebugSpinBox->SetMaxSliderValue( MinMaxSliderValues.Y );

		DebugSpinBox->SetMinValue( MinMaxValues.X );
		DebugSpinBox->SetMaxValue( MinMaxValues.Y );

		DebugSpinBox->SetValue( Value );

		DebugSpinBox->SetMinFractionalDigits( ( int32 )MinMaxFractDigits.X );
		DebugSpinBox->SetMaxFractionalDigits( ( int32 )MinMaxFractDigits.Y );

		DebugSpinBox->SetAlwaysUsesDeltaSnap( AlwaysSnapToDelta );
		DebugSpinBox->SetDelta( Delta );
	}
}

void UDebugSliderActivatableWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if( DebugSpinBox )
	{
		DebugSpinBox->OnValueChanged.AddDynamic( this, &UDebugSliderActivatableWidget::OnSliderValueChange );
	}
}

void UDebugSliderActivatableWidget::OnSliderValueChange( float NewValue )
{
	Value = NewValue;
	ValueChangeDelegate.ExecuteIfBound( NewValue );
}

float UDebugSliderActivatableWidget::GetValue() const
{
	return Value;
}

void UDebugSliderActivatableWidget::SetValue( float NewValue )
{
	Value = NewValue;
}